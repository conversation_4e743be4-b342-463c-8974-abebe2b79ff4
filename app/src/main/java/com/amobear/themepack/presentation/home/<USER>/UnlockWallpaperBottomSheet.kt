package com.amobear.themepack.presentation.home.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobear.themepack.R

/**
 * Enum representing different types of unlockable content
 */
enum class UnlockableContentType(
    val displayName: String,
    val priceDescription: String
) {
    WALLPAPER("Wallpaper", "wallpaper"),
    ICON_PACK("Icon pack", "icon pack"),
    WIDGET_PACK("Widget pack", "widget pack"),
    THEME("Theme", "theme")
}

/**
 * Generic bottom sheet to unlock different types of content with coins
 *
 * @param contentType The type of content being unlocked (wallpaper, icon pack, widget pack, etc.)
 * @param currentCoins User's current coin balance
 * @param requiredCoins Coins required to unlock the content
 * @param onDismiss Callback when the bottom sheet is dismissed
 * @param onUseCoins Callback when user chooses to use coins for unlocking
 * @param onGoPremium Callback when user chooses to go premium
 * @param contentTitle Optional specific title for the content (e.g., "Neon City Theme")
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UnlockContentBottomSheet(
    contentType: UnlockableContentType,
    currentCoins: Int,
    requiredCoins: Int,
    onDismiss: () -> Unit,
    onUseCoins: () -> Unit,
    onGoPremium: () -> Unit,
    contentTitle: String? = null
) {
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = bottomSheetState,
        containerColor = Color.White,
        contentColor = Color(0xFF212121),
        dragHandle = null
    ) {
        UnlockContentBottomSheetContent(
            contentType = contentType,
            requiredCoins = requiredCoins,
            currentCoins = currentCoins,
            onUseCoins = onUseCoins,
            onGoPremium = onGoPremium,
            contentTitle = contentTitle
        )
    }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use UnlockContentBottomSheet instead
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UnlockWallpaperBottomSheet(
    currentCoins: Int,
    requiredCoins: Int,
    onDismiss: () -> Unit,
    onUseCoins: () -> Unit,
    onGoPremium: () -> Unit
) {
    UnlockContentBottomSheet(
        contentType = UnlockableContentType.WALLPAPER,
        currentCoins = currentCoins,
        requiredCoins = requiredCoins,
        onDismiss = onDismiss,
        onUseCoins = onUseCoins,
        onGoPremium = onGoPremium
    )
}

@Composable
private fun UnlockContentBottomSheetContent(
    contentType: UnlockableContentType,
    requiredCoins: Int,
    currentCoins: Int,
    onUseCoins: () -> Unit,
    onGoPremium: () -> Unit,
    contentTitle: String? = null
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Current coins indicator - aligned to end following responsive design
        Card(
            modifier = Modifier
                .wrapContentSize()
                .padding(vertical = 2.dp)
                .align(Alignment.End),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F0F0))
        ) {
            Row(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(horizontal = 6.dp, vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_coin),
                    contentDescription = "Coins",
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "$currentCoins",
                    color = Color(0xFF520035),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Dynamic title based on content type
        Text(
            text = if (contentTitle != null) {
                "$contentTitle locked"
            } else {
                "${contentType.displayName} locked"
            },
            color = Color(0xFF212121),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Price section with responsive layout
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Price to unlock this ${contentType.priceDescription}:",
                color = Color(0xFF212121),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "$requiredCoins",
                    color = Color(0xFF212121),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )

                Spacer(modifier = Modifier.width(4.dp))

                Image(
                    painter = painterResource(id = R.drawable.ic_coin),
                    contentDescription = "Coins",
                    modifier = Modifier.size(16.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Use coins button with responsive design
        Button(
            onClick = onUseCoins,
            enabled = currentCoins >= requiredCoins,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF015BFF),
                disabledContainerColor = Color(0xFFAAAAAA)
            ),
            shape = RoundedCornerShape(8.dp),
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_download_coin),
                    contentDescription = "Download",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "Use $requiredCoins coins",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Go premium button with responsive design
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onGoPremium() }
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_premium),
                contentDescription = "Premium",
                tint = Color(0xFFFF59C4),
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = "Go Premium to unlock all",
                color = Color(0xFFFF59C4),
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                style = TextStyle(textDecoration = TextDecoration.Underline)
            )
        }

        // Add bottom padding for safe area
        Spacer(modifier = Modifier.height(16.dp))
    }
}

// Preview functions for the new generic component

@Preview(showBackground = true, name = "Wallpaper - Sufficient Coins")
@Composable
fun UnlockContentWallpaperPreview() {
    UnlockContentBottomSheetContent(
        contentType = UnlockableContentType.WALLPAPER,
        currentCoins = 150,
        requiredCoins = 100,
        onUseCoins = {},
        onGoPremium = {}
    )
}

@Preview(showBackground = true, name = "Icon Pack - Insufficient Coins")
@Composable
fun UnlockContentIconPackPreview() {
    UnlockContentBottomSheetContent(
        contentType = UnlockableContentType.ICON_PACK,
        currentCoins = 50,
        requiredCoins = 100,
        onUseCoins = {},
        onGoPremium = {}
    )
}

@Preview(showBackground = true, name = "Widget Pack - With Title")
@Composable
fun UnlockContentWidgetPackPreview() {
    UnlockContentBottomSheetContent(
        contentType = UnlockableContentType.WIDGET_PACK,
        currentCoins = 200,
        requiredCoins = 150,
        onUseCoins = {},
        onGoPremium = {},
        contentTitle = "Neon Clock Widget"
    )
}

@Preview(showBackground = true, name = "Theme - Sufficient Coins")
@Composable
fun UnlockContentThemePreview() {
    UnlockContentBottomSheetContent(
        contentType = UnlockableContentType.THEME,
        currentCoins = 300,
        requiredCoins = 250,
        onUseCoins = {},
        onGoPremium = {},
        contentTitle = "Aesthetic Pink Theme"
    )
}

// Legacy preview functions for backward compatibility

@Preview(showBackground = true, name = "Legacy - Wallpaper")
@Composable
fun UnlockWallpaperDialogPreview() {
    UnlockContentBottomSheetContent(
        contentType = UnlockableContentType.WALLPAPER,
        currentCoins = 150,
        requiredCoins = 100,
        onUseCoins = {},
        onGoPremium = {}
    )
}

@Preview(showBackground = true, name = "Legacy - Insufficient Coins")
@Composable
fun UnlockWallpaperDialogInsufficientCoinsPreview() {
    UnlockContentBottomSheetContent(
        contentType = UnlockableContentType.WALLPAPER,
        currentCoins = 50,
        requiredCoins = 100,
        onUseCoins = {},
        onGoPremium = {}
    )
}
